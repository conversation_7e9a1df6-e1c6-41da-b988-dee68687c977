import React, { useState, useRef } from 'react';
import { Phone, MessageCircle, Mail, Headphones, Copy, Check, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from '@/context/AuthContext';
import { useNavigate } from 'react-router-dom';
import EnhancedMSG91ChatWidget from '@/components/EnhancedMSG91ChatWidget';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

interface SupportButtonProps {
  className?: string;
  variant?: 'header' | 'admin';
}

const SupportButton: React.FC<SupportButtonProps> = ({
  className = '',
  variant = 'header'
}) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [showCallbackModal, setShowCallbackModal] = useState(false);
  const [showChatWidget, setShowChatWidget] = useState(false);
  const [showSignInPrompt, setShowSignInPrompt] = useState(false);
  const [signInPromptFeature, setSignInPromptFeature] = useState('');

  // Helper function to detect temporary emails
  const isTemporaryEmail = (email: string | undefined): boolean => {
    return email ? email.includes('@temp.grid2play.com') : false;
  };

  // Get clean email for display (empty if temporary)
  const getDisplayEmail = (): string => {
    return isTemporaryEmail(user?.email) ? '' : (user?.email || '');
  };

  const [callbackForm, setCallbackForm] = useState({
    email: getDisplayEmail(),
    phone: user?.phone || '',
    query: ''
  });
  const [copiedStates, setCopiedStates] = useState({
    phone: false,
    email: false
  });

  // Helper function to check authentication and show prompt if needed
  const requireAuth = (featureName: string, callback: () => void) => {
    if (!user) {
      setSignInPromptFeature(featureName);
      setShowSignInPrompt(true);
      return;
    }
    callback();
  };

  // Copy to clipboard functionality
  const copyToClipboard = async (text: string, type: 'phone' | 'email') => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedStates(prev => ({ ...prev, [type]: true }));
      
      toast({
        title: "Copied!",
        description: `${type === 'phone' ? 'Phone number' : 'Email address'} copied to clipboard`,
        duration: 2000,
      });

      // Reset copied state after 2 seconds
      setTimeout(() => {
        setCopiedStates(prev => ({ ...prev, [type]: false }));
      }, 2000);
    } catch (err) {
      toast({
        title: "Copy failed",
        description: "Please copy manually",
        variant: "destructive",
        duration: 2000,
      });
    }
  };

  // Handle callback request
  const handleCallbackRequest = () => {
    requireAuth('callback support', () => {
      setShowCallbackModal(true);
    });
  };

  // Handle WhatsApp chat
  const handleWhatsAppChat = () => {
    requireAuth('WhatsApp support', () => {
      const message = encodeURIComponent(
        `Hi Grid२Play Support! I need assistance with my account. User: ${user?.user_metadata?.full_name || user?.email || 'Guest'}`
      );
      window.open(`https://wa.me/************?text=${message}`, '_blank');
    });
  };

  // Handle email support
  const handleEmailSupport = () => {
    requireAuth('email support', () => {
      const subject = encodeURIComponent('Grid२Play Support Request');
      const displayEmail = getDisplayEmail() || 'Not provided';
      const body = encodeURIComponent(
        `Hi Grid२Play Support Team,\n\nI need assistance with:\n\n[Please describe your issue here]\n\nUser Details:\nName: ${user?.user_metadata?.full_name || 'Not provided'}\nEmail: ${displayEmail}\nPhone: ${user?.phone || 'Not provided'}\n\nThank you!`
      );
      window.location.href = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
    });
  };

  // Handle live chat
  const handleLiveChat = () => {
    requireAuth('live chat support', () => {
      setShowChatWidget(true);
    });
  };

  const supportOptions = [
    {
      icon: <Phone className="w-4 h-4" />,
      label: "Request a Callback Now",
      action: handleCallbackRequest,
      description: "We'll call you back in ~10 minutes"
    },
    {
      icon: <MessageCircle className="w-4 h-4" />,
      label: "Chat with Us on WhatsApp",
      action: handleWhatsAppChat,
      description: "Instant messaging support"
    },
    {
      icon: <Mail className="w-4 h-4" />,
      label: "Send Us an Email",
      action: handleEmailSupport,
      description: "Detailed support via email"
    },
    {
      icon: <Headphones className="w-4 h-4" />,
      label: "Live chat now",
      action: handleLiveChat,
      description: "Real-time chat support"
    }
  ];

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className={`
              bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 
              min-h-[44px] px-3 sm:px-4 py-2 
              flex items-center gap-2 
              text-xs sm:text-sm font-medium
              transition-all duration-200
              ${className}
            `}
          >
            <Phone className="w-4 h-4" />
            <span className="hidden sm:inline">Support</span>
          </Button>
        </DropdownMenuTrigger>
        
        <DropdownMenuContent 
          align="end" 
          className="w-80 sm:w-96 p-0 bg-white border border-gray-200 shadow-lg"
        >
          {/* Header */}
          <div className="bg-emerald-900 text-white p-3 sm:p-4">
            <h3 className="font-semibold text-sm sm:text-base">Support & Feedback</h3>
            <p className="text-emerald-200 text-xs sm:text-sm mt-1">
              We'd Love to Hear From You! Reach out via:
            </p>
          </div>

          {/* Support Options */}
          <div className="p-2">
            {supportOptions.map((option, index) => (
              <DropdownMenuItem
                key={index}
                onClick={option.action}
                className="
                  flex items-center gap-3 p-3 sm:p-4 
                  min-h-[44px] cursor-pointer
                  hover:bg-gray-50 rounded-md
                  focus:bg-gray-50
                "
              >
                <div className="text-emerald-900">{option.icon}</div>
                <div className="flex-1">
                  <div className="font-medium text-xs sm:text-sm text-gray-900">
                    {option.label}
                  </div>
                  <div className="text-xs text-gray-500 mt-0.5">
                    {option.description}
                  </div>
                </div>
              </DropdownMenuItem>
            ))}
          </div>

          <DropdownMenuSeparator className="my-2" />

          {/* Contact Information */}
          <div className="p-3 sm:p-4 bg-gray-50">
            <h4 className="font-medium text-xs sm:text-sm text-gray-900 mb-3">
              Direct Contact
            </h4>
            
            {/* Phone Number */}
            <div className="flex items-center justify-between mb-3 min-h-[44px]">
              <div className="flex items-center gap-2">
                <Phone className="w-3 h-3 sm:w-4 sm:h-4 text-gray-600" />
                <span className="text-xs sm:text-sm text-gray-700 font-mono">
                  +91 92118 48599
                </span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard('+91 92118 48599', 'phone')}
                className="min-h-[32px] min-w-[32px] p-1 hover:bg-gray-200"
                aria-label="Copy phone number"
              >
                {copiedStates.phone ? (
                  <Check className="w-3 h-3 text-green-600" />
                ) : (
                  <Copy className="w-3 h-3 text-gray-600" />
                )}
              </Button>
            </div>

            {/* Email Address */}
            <div className="flex items-center justify-between min-h-[44px]">
              <div className="flex items-center gap-2">
                <Mail className="w-3 h-3 sm:w-4 sm:h-4 text-gray-600" />
                <span className="text-xs sm:text-sm text-gray-700 font-mono">
                  <EMAIL>
                </span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard('<EMAIL>', 'email')}
                className="min-h-[32px] min-w-[32px] p-1 hover:bg-gray-200"
                aria-label="Copy email address"
              >
                {copiedStates.email ? (
                  <Check className="w-3 h-3 text-green-600" />
                ) : (
                  <Copy className="w-3 h-3 text-gray-600" />
                )}
              </Button>
            </div>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Callback Request Modal */}
      <Dialog open={showCallbackModal} onOpenChange={setShowCallbackModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-base sm:text-lg">Request a Callback</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-xs sm:text-sm text-gray-600">
              We are here to assist you! Our dedicated team will reach out to you in approximately 10 minutes.
            </p>
            
            <div className="space-y-3">
              <Input
                placeholder={isTemporaryEmail(user?.email) ? "Enter your email address" : "Email address"}
                value={callbackForm.email}
                onChange={(e) => setCallbackForm(prev => ({ ...prev, email: e.target.value }))}
                className="text-xs sm:text-sm"
              />
              <Input
                placeholder="Phone number"
                value={callbackForm.phone}
                onChange={(e) => setCallbackForm(prev => ({ ...prev, phone: e.target.value }))}
                className="text-xs sm:text-sm"
              />
              <Textarea
                placeholder="Specify your query here (optional)"
                value={callbackForm.query}
                onChange={(e) => setCallbackForm(prev => ({ ...prev, query: e.target.value }))}
                className="text-xs sm:text-sm min-h-[80px]"
              />
            </div>

            <Button 
              onClick={() => {
                toast({
                  title: "Callback Requested",
                  description: "We'll contact you within 10 minutes!",
                });
                setShowCallbackModal(false);
              }}
              className="w-full bg-emerald-900 hover:bg-emerald-800 text-white min-h-[44px]"
            >
              Submit
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Enhanced MSG91 Chat Widget - Use natural positioning like homepage */}
      {showChatWidget && (
        <>
          <EnhancedMSG91ChatWidget />
          {/* Optional close button overlay - positioned to not interfere with widget */}
          <Button
            onClick={() => setShowChatWidget(false)}
            className="fixed top-4 left-4 z-[60] bg-red-600 hover:bg-red-700 text-white"
            size="sm"
          >
            Close Chat
          </Button>
        </>
      )}

      {/* Sign-In Prompt Modal */}
      <Dialog open={showSignInPrompt} onOpenChange={setShowSignInPrompt}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-base sm:text-lg flex items-center gap-2">
              <User className="w-5 h-5 text-emerald-900" />
              Sign In Required
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-xs sm:text-sm text-gray-600">
              Please sign in to access <span className="font-semibold text-emerald-900">{signInPromptFeature}</span>.
              Sign in now to get personalized support.
            </p>
          </div>
          <DialogFooter className="flex gap-2 sm:gap-3">
            <Button
              variant="outline"
              onClick={() => setShowSignInPrompt(false)}
              className="flex-1 min-h-[44px] text-xs sm:text-sm"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setShowSignInPrompt(false);
                navigate('/login');
              }}
              className="flex-1 bg-emerald-900 hover:bg-emerald-800 text-white min-h-[44px] text-xs sm:text-sm"
            >
              Sign In
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default SupportButton;
