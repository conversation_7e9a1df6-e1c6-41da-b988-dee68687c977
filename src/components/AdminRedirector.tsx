
import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { useIsMobile } from '@/hooks/use-mobile';

const AdminRedirector = () => {
  const { user, userRole } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const isMobile = useIsMobile();

  useEffect(() => {
    if (user && (userRole === 'admin' || userRole === 'super_admin')) {
      // Check if user is on mobile and needs mobile admin interface
      if (isMobile) {
        // If on mobile but not on mobile admin routes, redirect to mobile home
        if (!location.pathname.startsWith('/admin/') || location.pathname === '/admin') {
          navigate('/admin/mobile-home', { replace: true });
          return;
        }
        // If already on a mobile admin route, don't redirect
        if (location.pathname.startsWith('/admin/') && location.pathname !== '/admin/') {
          return;
        }
      } else {
        // Desktop logic
        // Don't redirect if already on a valid desktop admin route
        if (location.pathname.startsWith('/admin/')) {
          // If on mobile route but now on desktop, redirect to desktop admin
          if (location.pathname.includes('-mobile')) {
            navigate('/admin#dashboard', { replace: true });
            return;
          }
          return;
        }

        // Redirect non-admin routes to desktop admin
        if (!location.pathname.startsWith('/admin')) {
          navigate('/admin#dashboard', { replace: true });
          return;
        }

        // If at exactly /admin without hash, add dashboard hash
        if (location.pathname === '/admin' && !location.hash) {
          navigate('/admin#dashboard', { replace: true });
        }
      }
    }
  }, [user, userRole, location, navigate, isMobile]);

  return null;
};

export default AdminRedirector;
